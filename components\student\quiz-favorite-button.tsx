'use client'

import { useState, useEffect } from 'react'
import { Heart } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

interface QuizFavoriteButtonProps {
  quizId: string
  className?: string
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  showText?: boolean
}

export function QuizFavoriteButton({ 
  quizId, 
  className,
  variant = 'outline',
  size = 'default',
  showText = true
}: QuizFavoriteButtonProps) {
  const [isFavorited, setIsFavorited] = useState(false)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    checkFavoriteStatus()
  }, [quizId])

  const checkFavoriteStatus = async () => {
    try {
      const response = await fetch(`/api/student/quizzes/${quizId}/favorite`)
      if (response.ok) {
        const data = await response.json()
        setIsFavorited(data.isFavorited)
      }
    } catch (error) {
      console.error('Error checking favorite status:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleFavorite = async () => {
    setUpdating(true)
    try {
      const method = isFavorited ? 'DELETE' : 'POST'
      const response = await fetch(`/api/student/quizzes/${quizId}/favorite`, {
        method
      })

      if (response.ok) {
        setIsFavorited(!isFavorited)
        toast.success(
          isFavorited 
            ? 'Quiz removed from favorites' 
            : 'Quiz added to favorites'
        )
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to update favorites')
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
      toast.error('Failed to update favorites')
    } finally {
      setUpdating(false)
    }
  }

  if (loading) {
    return (
      <Button
        variant={variant}
        size={size}
        disabled
        className={className}
      >
        <Heart className="h-4 w-4" />
        {showText && size !== 'icon' && (
          <span className="ml-2">Loading...</span>
        )}
      </Button>
    )
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleFavorite}
      disabled={updating}
      className={cn(
        'transition-colors',
        isFavorited && variant === 'outline' && 'border-red-500 text-red-500 hover:bg-red-50',
        className
      )}
    >
      <Heart 
        className={cn(
          'h-4 w-4 transition-colors',
          isFavorited ? 'fill-red-500 text-red-500' : 'text-current'
        )} 
      />
      {showText && size !== 'icon' && (
        <span className="ml-2">
          {updating 
            ? 'Updating...' 
            : isFavorited 
              ? 'Favorited' 
              : 'Add to Favorites'
          }
        </span>
      )}
    </Button>
  )
}
