import { NextRequest } from 'next/server'
 import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  totalQuizzes: number
  totalAttempts: number
  averageScore: number
  recentActivity: Array<{
    id: string
    type: 'quiz_created' | 'quiz_attempt' | 'user_registered'
    title: string
    description: string
    timestamp: string
    user?: {
      name: string
      email: string
    }
  }>
  trends: {
    usersGrowth: number
    quizzesGrowth: number
    attemptsGrowth: number
    scoreImprovement: number
  }
}

// GET /api/admin/dashboard/stats - Get dashboard statistics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
     
  },
  async (request: NextRequest) => {
    try {
      // Get current date ranges for comparison
      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)

      // Parallel queries for better performance
      const [
        totalUsers,
        totalUsersLastMonth,
        activeUsers,
        totalQuizzes,
        totalQuizzesLastMonth,
        totalAttempts,
        totalAttemptsLastMonth,
        averageScoreResult,
        averageScoreLastMonth,
        recentQuizzes,
        recentAttempts,
        recentUsers
      ] = await Promise.all([
        // Total users
        prisma.user.count(),
        
        // Total users last month (for growth calculation)
        prisma.user.count({
          where: {
            createdAt: {
              lt: thirtyDaysAgo
            }
          }
        }),

        // Active users (users with attempts in last 30 days)
        prisma.user.count({
          where: {
            quizAttempts: {
              some: {
                startedAt: {
                  gte: thirtyDaysAgo
                }
              }
            }
          }
        }),

        // Total quizzes
        prisma.quiz.count(),

        // Total quizzes last month
        prisma.quiz.count({
          where: {
            createdAt: {
              lt: thirtyDaysAgo
            }
          }
        }),

        // Total attempts
        prisma.quizAttempt.count(),

        // Total attempts last month
        prisma.quizAttempt.count({
          where: {
            startedAt: {
              lt: thirtyDaysAgo
            }
          }
        }),

        // Average score (current)
        prisma.quizAttempt.aggregate({
          _avg: {
            percentage: true
          },
          where: {
            isCompleted: true,
            startedAt: {
              gte: thirtyDaysAgo
            }
          }
        }),

        // Average score (last month)
        prisma.quizAttempt.aggregate({
          _avg: {
            percentage: true
          },
          where: {
            isCompleted: true,
            startedAt: {
              gte: sixtyDaysAgo,
              lt: thirtyDaysAgo
            }
          }
        }),

        // Recent quizzes for activity feed
        prisma.quiz.findMany({
          take: 5,
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            creator: {
              select: {
                name: true,
                email: true
              }
            }
          }
        }),

        // Recent attempts for activity feed
        prisma.quizAttempt.findMany({
          take: 5,
          orderBy: {
            startedAt: 'desc'
          },
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            },
            quiz: {
              select: {
                title: true
              }
            }
          }
        }),

        // Recent users for activity feed
        prisma.user.findMany({
          take: 5,
          orderBy: {
            createdAt: 'desc'
          },
          where: {
            role: 'STUDENT'
          }
        })
      ])

      // Calculate growth percentages
      const usersGrowth = totalUsersLastMonth > 0 
        ? Math.round(((totalUsers - totalUsersLastMonth) / totalUsersLastMonth) * 100)
        : 0

      const quizzesGrowth = totalQuizzesLastMonth > 0
        ? Math.round(((totalQuizzes - totalQuizzesLastMonth) / totalQuizzesLastMonth) * 100)
        : 0

      const attemptsGrowth = totalAttemptsLastMonth > 0
        ? Math.round(((totalAttempts - totalAttemptsLastMonth) / totalAttemptsLastMonth) * 100)
        : 0

      const currentAvgScore = averageScoreResult._avg.percentage || 0
      const lastMonthAvgScore = averageScoreLastMonth._avg.percentage || 0
      const scoreImprovement = lastMonthAvgScore > 0
        ? Math.round(((currentAvgScore - lastMonthAvgScore) / lastMonthAvgScore) * 100)
        : 0

      // Build recent activity feed
      const recentActivity: Array<{
        id: string
        type: 'quiz_created' | 'quiz_attempt' | 'user_registered'
        title: string
        description: string
        timestamp: string
        user?: {
          name: string
          email: string
        }
      }> = []

      // Add recent quizzes
      recentQuizzes.forEach(quiz => {
        recentActivity.push({
          id: quiz.id,
          type: 'quiz_created' as const,
          title: 'New Quiz Created',
          description: `"${quiz.title}" was created`,
          timestamp: quiz.createdAt.toISOString(),
          user: {
            name: quiz.creator.name || 'Unknown',
            email: quiz.creator.email || ''
          }
        })
      })

      // Add recent attempts
      recentAttempts.forEach(attempt => {
        recentActivity.push({
          id: attempt.id,
          type: 'quiz_attempt' as const,
          title: 'Quiz Completed',
          description: `${attempt.user.name || 'Unknown'} completed "${attempt.quiz.title}" with ${Math.round(attempt.percentage)}%`,
          timestamp: attempt.startedAt.toISOString(),
          user: {
            name: attempt.user.name || 'Unknown',
            email: attempt.user.email || ''
          }
        })
      })

      // Add recent users
      recentUsers.forEach(user => {
        recentActivity.push({
          id: user.id,
          type: 'user_registered' as const,
          title: 'New User Registered',
          description: `${user.name} joined the platform`,
          timestamp: user.createdAt.toISOString(),
          user: {
            name: user.name || 'Unknown',
            email: user.email || ''
          }
        })
      })

      // Sort activity by timestamp (most recent first)
      recentActivity.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

      const stats: DashboardStats = {
        totalUsers,
        activeUsers,
        totalQuizzes,
        totalAttempts,
        averageScore: Math.round(currentAvgScore),
        recentActivity: recentActivity.slice(0, 10), // Limit to 10 most recent
        trends: {
          usersGrowth,
          quizzesGrowth,
          attemptsGrowth,
          scoreImprovement
        }
      }

      return APIResponse.success(stats, 'Dashboard statistics retrieved successfully')
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      return APIResponse.error('Failed to fetch dashboard statistics', 500)
    }
  }
)
