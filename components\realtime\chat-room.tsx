"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Send, 
  Users, 
  MessageCircle, 
  Smile, 
  Paperclip,
  MoreHorizontal,
  UserPlus,
  Settings
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getSocketClient, ChatMessage } from "@/lib/socket-client"
import { useSession } from "next-auth/react"
import { toast } from "sonner"

interface ChatRoomProps {
  roomId: string
  roomName: string
  roomType?: 'public' | 'private' | 'quiz'
  className?: string
}

interface TypingUser {
  userId: string
  userName: string
}

export function ChatRoom({
  roomId,
  roomName,
  roomType = 'public',
  className = ""
}: ChatRoomProps) {
  const { data: session } = useSession()
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState("")
  const [isConnected, setIsConnected] = useState(false)
  const [onlineUsers, setOnlineUsers] = useState<string[]>([])
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([])
  const [currentUser, setCurrentUser] = useState<string | null>(null)
  const [isTyping, setIsTyping] = useState(false)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (!session?.user?.id || currentUser) return

    const socketClient = getSocketClient()

    // Get current user ID from session (only once)
    setCurrentUser(session.user.id)

    // Load message history
    const loadMessageHistory = async () => {
      try {
        const response = await fetch(`/api/chat/messages?roomId=${roomId}&limit=50`)
        if (response.ok) {
          const data = await response.json()
          if (data.success && data.data.messages) {
            setMessages(data.data.messages.map((msg: any) => ({
              id: msg.id,
              userId: msg.userId,
              userName: msg.user.name || 'Unknown',
              message: msg.message,
              type: msg.type,
              timestamp: new Date(msg.createdAt),
              roomId: msg.roomId
            })))
          }
        }
      } catch (error) {
        console.error('Error loading message history:', error)
      }
    }

    loadMessageHistory()

    // Join chat room
    socketClient.joinChatRoom(roomId)

    // Connection status
    const handleConnection = () => {
      setIsConnected(true)
    }

    const handleDisconnection = () => {
      setIsConnected(false)
    }

    // Chat events
    const handleUserJoined = (data: { userId: string, name: string }) => {
      setOnlineUsers(prev => [...prev.filter(id => id !== data.userId), data.userId])
      toast(`${data.name} joined the chat`, {
        icon: <UserPlus className="h-4 w-4" />
      })
    }

    const handleMessageReceived = (message: ChatMessage) => {
      // Only handle messages for this specific room
      if (message.roomId !== roomId) {
        return
      }

      console.log('📥 Message received for room', roomId, ':', message)
      setMessages(prev => [...prev, message])

      // Remove typing indicator for this user
      setTypingUsers(prev => prev.filter(u => u.userId !== message.userId))
    }

    const handleUserTyping = (data: { userId: string, userName: string, isTyping: boolean }) => {
      if (data.userId === currentUser) return // Don't show own typing

      setTypingUsers(prev => {
        if (data.isTyping) {
          return prev.find(u => u.userId === data.userId) 
            ? prev 
            : [...prev, { userId: data.userId, userName: data.userName }]
        } else {
          return prev.filter(u => u.userId !== data.userId)
        }
      })
    }

    // Set up event listeners
    socketClient.on('connection:established', handleConnection)
    socketClient.on('connection:lost', handleDisconnection)
    socketClient.on('chat:user_joined', handleUserJoined)
    socketClient.on('chat:message_received', handleMessageReceived)
    socketClient.on('chat:user_typing', handleUserTyping)

    // Initial connection status
    setIsConnected(socketClient.isConnected())

    // Cleanup
    return () => {
      console.log('🧹 Cleaning up ChatRoom for', roomId)
      socketClient.off('connection:established', handleConnection)
      socketClient.off('connection:lost', handleDisconnection)
      socketClient.off('chat:user_joined', handleUserJoined)
      socketClient.off('chat:message_received', handleMessageReceived)
      socketClient.off('chat:user_typing', handleUserTyping)

      // Leave the room when component unmounts
      socketClient.leaveChatRoom(roomId)

      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }, [roomId, session?.user?.id, currentUser])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const sendMessage = () => {
    if (!newMessage.trim() || !isConnected) {
      console.log('❌ Cannot send message:', { hasMessage: !!newMessage.trim(), isConnected })
      return
    }

    console.log('📤 Sending message:', { roomId, message: newMessage.trim(), currentUser })
    const socketClient = getSocketClient()
    socketClient.sendChatMessage({
      roomId,
      message: newMessage.trim(),
      type: 'text'
    })

    setNewMessage("")

    // Stop typing indicator
    if (isTyping) {
      socketClient.setTyping(roomId, false)
      setIsTyping(false)
    }
  }

  const handleInputChange = (value: string) => {
    setNewMessage(value)

    if (!isConnected) return

    const socketClient = getSocketClient()

    // Start typing indicator
    if (!isTyping && value.trim()) {
      socketClient.setTyping(roomId, true)
      setIsTyping(true)
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        socketClient.setTyping(roomId, false)
        setIsTyping(false)
      }
    }, 2000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getRoomTypeColor = () => {
    switch (roomType) {
      case 'private':
        return 'text-purple-600 bg-purple-50 border-purple-200'
      case 'quiz':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200'
    }
  }

  return (
    <div className={`flex flex-col h-96 ${className}`}>
      <Card className="flex-1 flex flex-col">
        {/* Header */}
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                {roomName}
              </CardTitle>
              <CardDescription className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`} />
                {isConnected ? 'Connected' : 'Disconnected'}
                {onlineUsers.length > 0 && (
                  <>
                    <span>•</span>
                    <span>{onlineUsers.length} online</span>
                  </>
                )}
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge className={getRoomTypeColor()}>
                {roomType}
              </Badge>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Messages */}
        <CardContent className="flex-1 flex flex-col p-0">
          <ScrollArea className="flex-1 px-4">
            <div className="space-y-4 py-4">
              <AnimatePresence>
                {messages.map((message, index) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className={`flex gap-3 ${
                      message.userId === currentUser ? 'flex-row-reverse' : ''
                    }`}
                  >
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      <AvatarFallback className="text-xs">
                        {message.userName.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className={`flex-1 max-w-xs ${
                      message.userId === currentUser ? 'text-right' : ''
                    }`}>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium">
                          {message.userId === currentUser ? 'You' : message.userName}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatTime(message.timestamp)}
                        </span>
                      </div>
                      
                      <div className={`inline-block p-3 rounded-lg text-sm ${
                        message.userId === currentUser
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      }`}>
                        {message.message}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Typing indicators */}
              {typingUsers.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex gap-3"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="text-xs">
                      {typingUsers[0].userName.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <div className="text-sm text-muted-foreground mb-1">
                      {typingUsers.length === 1 
                        ? `${typingUsers[0].userName} is typing...`
                        : `${typingUsers.length} people are typing...`
                      }
                    </div>
                    <div className="inline-block p-3 rounded-lg bg-muted">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Message Input */}
          <div className="p-4 border-t">
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Input
                  value={newMessage}
                  onChange={(e) => handleInputChange(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={isConnected ? "Type a message..." : "Connecting..."}
                  disabled={!isConnected}
                  className="pr-20"
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-1">
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Smile className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Paperclip className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <Button 
                onClick={sendMessage}
                disabled={!newMessage.trim() || !isConnected}
                size="sm"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
