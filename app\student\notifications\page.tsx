"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import {
  Bell,
  CheckCircle,
  AlertCircle,
  Info,
  XCircle,
  Wifi,
  WifiOff,
  Users,
  Activity,
  Clock,
  Trash2,
  TestTube
} from "lucide-react"
import { NotificationCenter } from "@/components/realtime/notification-center"
import { ConnectionStatus } from "@/components/realtime/connection-status"
import { NotificationTest } from "@/components/student/notification-test"
import { getSocketClient, initializeSocket } from "@/lib/socket-client"
import { toast } from "sonner"

interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  createdAt: string
  read: boolean
  data?: any
}

export default function StudentNotificationsPage() {
  const { data: session } = useSession()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting')
  const [isSocketInitialized, setIsSocketInitialized] = useState(false)

  useEffect(() => {
    if (session?.user && !isSocketInitialized) {
      console.log('🔌 Initializing socket for student notifications')
      
      const socketClient = initializeSocket({
        id: session.user.id,
        name: session.user.name || 'Student',
        email: session.user.email || '',
        role: session.user.role || 'STUDENT'
      })

      if (socketClient) {
        // Listen for connection events
        socketClient.on('connection:established', () => {
          setConnectionStatus('connected')
          toast.success('Connected to real-time notifications')
        })

        socketClient.on('connection:lost', () => {
          setConnectionStatus('disconnected')
          toast.error('Lost connection to notification server')
        })

        socketClient.on('authenticated', (data) => {
          console.log('✅ Student authenticated:', data)
          setConnectionStatus('connected')
        })

        // Listen for notifications
        socketClient.on('notification:received', (notification: Notification) => {
          console.log('📨 Notification received:', notification)
          setNotifications(prev => [notification, ...prev])
          
          // Show toast notification
          switch (notification.type) {
            case 'success':
              toast.success(notification.title, { description: notification.message })
              break
            case 'error':
              toast.error(notification.title, { description: notification.message })
              break
            case 'warning':
              toast.warning(notification.title, { description: notification.message })
              break
            default:
              toast.info(notification.title, { description: notification.message })
          }
        })

        setIsSocketInitialized(true)
      }
    }
  }, [session, isSocketInitialized])

  // Fetch existing notifications
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const response = await fetch('/api/notifications?limit=50')
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setNotifications(data.data || [])
          }
        }
      } catch (error) {
        console.error('Error fetching notifications:', error)
      }
    }

    fetchNotifications()
  }, [])

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST'
      })
      
      if (response.ok) {
        setNotifications(prev => 
          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
        )
      }
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setNotifications(prev => prev.filter(n => n.id !== notificationId))
        toast.success('Notification deleted')
      }
    } catch (error) {
      console.error('Error deleting notification:', error)
      toast.error('Failed to delete notification')
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error': return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-500" />
      default: return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Notifications</h1>
          <p className="text-muted-foreground">
            Stay updated with real-time notifications and system updates
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'}>
            {connectionStatus === 'connected' ? (
              <>
                <Wifi className="h-4 w-4 mr-1" />
                Connected
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 mr-1" />
                Disconnected
              </>
            )}
          </Badge>
          {unreadCount > 0 && (
            <Badge variant="secondary">
              {unreadCount} unread
            </Badge>
          )}
        </div>
      </div>

      <Tabs defaultValue="notifications" className="space-y-6">
        <TabsList>
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="connection">
            <Activity className="h-4 w-4 mr-2" />
            Connection Status
          </TabsTrigger>
          <TabsTrigger value="test">
            <TestTube className="h-4 w-4 mr-2" />
            Test
          </TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-4">
          {notifications.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No notifications yet</h3>
                <p className="text-muted-foreground text-center">
                  You'll receive real-time notifications here when there are updates
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {notifications.map((notification) => (
                <Card key={notification.id} className={`transition-all ${!notification.read ? 'border-primary/50 bg-primary/5' : ''}`}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        {getNotificationIcon(notification.type)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium">{notification.title}</h4>
                            {!notification.read && (
                              <Badge variant="secondary" className="h-5 px-2 text-xs">
                                New
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {new Date(notification.createdAt).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {!notification.read && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => markAsRead(notification.id)}
                          >
                            Mark as Read
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => deleteNotification(notification.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="connection" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Real-time Connection Status
              </CardTitle>
              <CardDescription>
                Monitor your connection to the real-time notification system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ConnectionStatus />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Notification Center</CardTitle>
              <CardDescription>
                Real-time notification management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NotificationCenter />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          <NotificationTest />
        </TabsContent>
      </Tabs>
    </div>
  )
}
