"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import { NotificationCenter } from "@/components/realtime/notification-center"
import { initializeSocket } from "@/lib/socket-client"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  LayoutDashboard,
  BookOpen,
  Trophy,
  User,
  Settings,
  Menu,
  Play,
  Calendar,
  BarChart3,
  Award,
  Clock,
  Target,
  LogOut,
  Moon,
  Sun,
  ChevronDown,
  Bell,
  MessageCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"

interface StudentLayoutProps {
  children: React.ReactNode
}

interface UserData {
  name: string
  email: string
  image?: string
  level: number
  totalPoints: number
}

const navigationItems = [
  {
    title: "Dashboard",
    href: "/student",
    icon: LayoutDashboard,
    description: "Overview and progress"
  },
  {
    title: "Browse Quizzes",
    href: "/student/browse",
    icon: BookOpen,
    description: "Find and take quizzes"
  },
  {
    title: "Daily Practice",
    href: "/student/practice",
    icon: Play,
    description: "Quick practice sessions"
  },
  {
    title: "My History",
    href: "/student/history",
    icon: Clock,
    description: "Past attempts and scores"
  },
  {
    title: "Analytics",
    href: "/student/analytics",
    icon: BarChart3,
    description: "Performance insights"
  },
  {
    title: "Achievements",
    href: "/student/achievements",
    icon: Award,
    description: "Badges and milestones"
  },
  {
    title: "Leaderboard",
    href: "/student/leaderboard",
    icon: Trophy,
    description: "Rankings and competition"
  },
  {
    title: "Schedule",
    href: "/student/schedule",
    icon: Calendar,
    description: "Upcoming quizzes"
  },
  {
    title: "Notifications",
    href: "/student/notifications",
    icon: Bell,
    description: "Real-time notifications and updates"
  },
  {
    title: "Chat",
    href: "/student/chat",
    icon: MessageCircle,
    description: "Real-time messaging and discussions"
  }
]

export function StudentLayout({ children }: StudentLayoutProps) {
  const pathname = usePathname()
  const { theme, setTheme } = useTheme()
  const { data: session } = useSession()
  const [userData, setUserData] = useState<UserData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchUserData = async () => {
      if (!session?.user?.id) {
        setLoading(false)
        return
      }

      try {
        const response = await fetch('/api/student/profile')
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            // Calculate level from total points
            const calculateLevel = (points: number): number => {
              return Math.floor(Math.sqrt(points / 100)) + 1
            }

            setUserData({
              name: data.data.name || session.user.name || 'Student',
              email: data.data.email || session.user.email || '',
              image: data.data.avatar || session.user.image,
              level: calculateLevel(data.data.stats?.totalPoints || 0),
              totalPoints: data.data.stats?.totalPoints || 0
            })

            // Initialize socket connection for real-time notifications
            if (session.user) {
              console.log('🔌 Initializing socket for student:', session.user.name)
              initializeSocket({
                id: session.user.id,
                name: session.user.name || 'Student',
                email: session.user.email || '',
                role: session.user.role || 'STUDENT',
                token: 'session-token'
              })
            }
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchUserData()
  }, [session])

  const NavItems = ({ mobile = false }: { mobile?: boolean }) => (
    <div className="space-y-2">
      {navigationItems.map((item) => {
        const isActive = pathname === item.href
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:bg-accent hover:text-accent-foreground",
              isActive ? "bg-accent text-accent-foreground font-medium" : "text-muted-foreground",
              mobile && "text-base py-3"
            )}
          >
            <item.icon className="h-4 w-4" />
            <div className="flex-1">
              <div>{item.title}</div>
              {mobile && (
                <div className="text-xs text-muted-foreground">{item.description}</div>
              )}
            </div>
            {isActive && (
              <div className="w-2 h-2 bg-primary rounded-full" />
            )}
          </Link>
        )
      })}
    </div>
  )

  return (
    <div className="min-h-screen bg-background">
      {/* Desktop Sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 z-50 border-r bg-background">
        <div className="flex flex-col flex-1 min-h-0">
          {/* Logo */}
          <div className="flex items-center h-16 px-6 border-b">
            <Link href="/student" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Target className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="font-bold text-lg">QuizMaster</h1>
                <p className="text-xs text-muted-foreground">Student Portal</p>
              </div>
            </Link>
          </div>



          {/* Navigation */}
          <ScrollArea className="flex-1 px-4 py-4">
            <NavItems />
          </ScrollArea>

          {/* User Profile */}
          <div className="p-4 border-t">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="w-full justify-start p-2">
                  <Avatar className="h-8 w-8 mr-3">
                    <AvatarImage src={userData?.image || " "} />
                    <AvatarFallback>
                      {loading ? "..." : (userData?.name?.split(' ').map(n => n[0]).join('') || "ST")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 text-left">
                    <div className="text-sm font-medium">
                      {loading ? "Loading..." : (userData?.name || "Student")}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {loading ? "..." : `Level ${userData?.level || 1} • ${userData?.totalPoints?.toLocaleString() || 0} pts`}
                    </div>
                  </div>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/student/profile">
                    <User className="h-4 w-4 mr-2" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/student/settings">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setTheme(theme === "dark" ? "light" : "dark")}>
                  {theme === "dark" ? (
                    <Sun className="h-4 w-4 mr-2" />
                  ) : (
                    <Moon className="h-4 w-4 mr-2" />
                  )}
                  Toggle Theme
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="md:pl-64">
        {/* Desktop Header */}
        <div className="hidden md:flex h-16 items-center justify-between gap-4 px-6 border-b bg-background">
          <div className="flex-1">
            <h2 className="text-lg font-semibold">Student Dashboard</h2>
          </div>

          <div className="flex items-center gap-2">
            <NotificationCenter />
          </div>
        </div>

        {/* Mobile Header */}
        <div className="flex h-16 items-center gap-4 px-4 md:hidden border-b bg-background">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon">
                <Menu className="h-4 w-4" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              <div className="flex items-center gap-2 px-2 py-4">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Target className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h1 className="font-bold text-lg">QuizMaster</h1>
                  <p className="text-xs text-muted-foreground">Student Portal</p>
                </div>
              </div>
              


              <ScrollArea className="flex-1 px-2 py-4">
                <NavItems mobile />
              </ScrollArea>

              <div className="px-2 py-4 border-t">
                <div className="flex items-center gap-3 p-2">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={userData?.image || " "} />
                    <AvatarFallback>
                      {loading ? "..." : (userData?.name?.split(' ').map(n => n[0]).join('') || "ST")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="text-sm font-medium">
                      {loading ? "Loading..." : (userData?.name || "Student")}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {loading ? "..." : `Level ${userData?.level || 1} • ${userData?.totalPoints?.toLocaleString() || 0} pts`}
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          <div className="flex-1">
            <Link href="/student" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Target className="h-5 w-5 text-white" />
              </div>
              <h1 className="font-bold text-lg">QuizMaster</h1>
            </Link>
          </div>

          <div className="flex items-center gap-2">
            <NotificationCenter />
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={userData?.image || " "} />
                    <AvatarFallback>
                      {loading ? "..." : (userData?.name?.split(' ').map(n => n[0]).join('') || "ST")}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/student/profile">
                    <User className="h-4 w-4 mr-2" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/student/settings">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setTheme(theme === "dark" ? "light" : "dark")}>
                  {theme === "dark" ? (
                    <Sun className="h-4 w-4 mr-2" />
                  ) : (
                    <Moon className="h-4 w-4 mr-2" />
                  )}
                  Toggle Theme
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Page Content */}
        <main className="flex-1">
          {children}
        </main>
      </div>
    </div>
  )
}
