"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Zap, 
  Bell, 
  MessageCircle, 
  Users, 
  Send,
  Settings,
  Activity,
  Wifi,
  TestTube
} from "lucide-react"
import { NotificationCenter } from "@/components/realtime/notification-center"
import { LiveQuizSession } from "@/components/realtime/live-quiz-session"
import { ChatRoom } from "@/components/realtime/chat-room"
import { ConnectionStatus } from "@/components/realtime/connection-status"
import { getSocketClient, initializeSocket } from "@/lib/socket-client"
import { toast } from "sonner"

export default function RealtimePage() {
  const [isInitialized, setIsInitialized] = useState(false)
  const [testNotification, setTestNotification] = useState({
    type: 'info' as 'info' | 'success' | 'warning' | 'error',
    title: '',
    message: '',
    targetUserId: ''
  })

  useEffect(() => {
    // Initialize socket connection with mock user data
    const mockUserData = {
      id: 'admin-user-1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      token: 'mock-jwt-token'
    }

    initializeSocket(mockUserData)
    setIsInitialized(true)
  }, [])

  const sendTestNotification = () => {
    if (!testNotification.title || !testNotification.message) {
      toast.error('Please fill in title and message')
      return
    }

    const socketClient = getSocketClient()

    if (testNotification.targetUserId) {
      // Send to specific user
      socketClient.sendNotification({
        targetUserId: testNotification.targetUserId,
        type: testNotification.type,
        title: testNotification.title,
        message: testNotification.message,
        data: { source: 'admin-panel' }
      })
      toast.success('Notification sent to specific user')
    } else {
      // Broadcast to all users
      socketClient.broadcastNotification({
        type: testNotification.type,
        title: testNotification.title,
        message: testNotification.message,
        data: { source: 'admin-panel' }
      })
      toast.success('Notification broadcasted to all users')
    }

    // Reset form
    setTestNotification({
      type: 'info',
      title: '',
      message: '',
      targetUserId: ''
    })
  }

  const sendTestQuizUpdate = () => {
    const socketClient = getSocketClient()
    socketClient.sendQuizProgress({
      quizId: 'demo-quiz-1',
      questionIndex: Math.floor(Math.random() * 10),
      timeRemaining: Math.floor(Math.random() * 300) + 60,
      answered: Math.random() > 0.5
    })
    toast.success('Test quiz progress sent')
  }

  if (!isInitialized) {
    return (
      <div className="p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Initializing real-time connection...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Zap className="h-8 w-8 text-orange-500" />
            Real-time Features
          </h1>
          <p className="text-muted-foreground mt-1">
            WebSocket-powered real-time communication and collaboration
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <ConnectionStatus />
          <NotificationCenter />
        </div>
      </div>

      {/* Connection Status Card */}
      <ConnectionStatus showDetails={true} />

      {/* Real-time Features */}
      <Tabs defaultValue="notifications" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="quiz" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Live Quiz
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4" />
            Chat
          </TabsTrigger>
          <TabsTrigger value="testing" className="flex items-center gap-2">
            <TestTube className="h-4 w-4" />
            Testing
          </TabsTrigger>
        </TabsList>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Send Notification */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Send className="h-5 w-5" />
                  Send Notification
                </CardTitle>
                <CardDescription>
                  Send real-time notifications to users
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Type</label>
                  <Select 
                    value={testNotification.type} 
                    onValueChange={(value: any) => setTestNotification(prev => ({ ...prev, type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="warning">Warning</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Title</label>
                  <Input
                    value={testNotification.title}
                    onChange={(e) => setTestNotification(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Notification title"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Message</label>
                  <Textarea
                    value={testNotification.message}
                    onChange={(e) => setTestNotification(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Notification message"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Target User ID (optional)</label>
                  <Input
                    value={testNotification.targetUserId}
                    onChange={(e) => setTestNotification(prev => ({ ...prev, targetUserId: e.target.value }))}
                    placeholder="Leave empty to broadcast to all users"
                  />
                </div>

                <Button onClick={sendTestNotification} className="w-full">
                  <Send className="h-4 w-4 mr-2" />
                  Send Notification
                </Button>
              </CardContent>
            </Card>

            {/* Notification Examples */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Examples</CardTitle>
                <CardDescription>
                  Send common notification types
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    const socketClient = getSocketClient()
                    socketClient.broadcastNotification({
                      type: 'success',
                      title: 'System Update',
                      message: 'The platform has been successfully updated with new features!'
                    })
                  }}
                >
                  <Badge className="bg-green-500 mr-2">Success</Badge>
                  System Update
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    const socketClient = getSocketClient()
                    socketClient.broadcastNotification({
                      type: 'warning',
                      title: 'Maintenance Notice',
                      message: 'Scheduled maintenance will begin in 30 minutes.'
                    })
                  }}
                >
                  <Badge className="bg-yellow-500 mr-2">Warning</Badge>
                  Maintenance Notice
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    const socketClient = getSocketClient()
                    socketClient.broadcastNotification({
                      type: 'info',
                      title: 'New Quiz Available',
                      message: 'A new JavaScript fundamentals quiz has been published!'
                    })
                  }}
                >
                  <Badge className="bg-blue-500 mr-2">Info</Badge>
                  New Quiz Available
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    const socketClient = getSocketClient()
                    socketClient.broadcastNotification({
                      type: 'error',
                      title: 'Service Alert',
                      message: 'Some features may be temporarily unavailable.'
                    })
                  }}
                >
                  <Badge className="bg-red-500 mr-2">Error</Badge>
                  Service Alert
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Live Quiz Tab */}
        <TabsContent value="quiz" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <LiveQuizSession
                quizId="demo-quiz-1"
                quizTitle="JavaScript Fundamentals Demo"
                totalQuestions={25}
                duration={45}
                onJoin={() => toast.success('Joined demo quiz session')}
                onLeave={() => toast.info('Left demo quiz session')}
              />
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Quiz Controls</CardTitle>
                  <CardDescription>
                    Test quiz session features
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={sendTestQuizUpdate}
                  >
                    Send Progress Update
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      const socketClient = getSocketClient()
                      socketClient.completeQuiz({
                        quizId: 'demo-quiz-1',
                        score: Math.floor(Math.random() * 40) + 60,
                        timeSpent: Math.floor(Math.random() * 30) + 15,
                        rank: Math.floor(Math.random() * 10) + 1
                      })
                      toast.success('Test quiz completion sent')
                    }}
                  >
                    Complete Quiz (Test)
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Session Stats</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span>Active Sessions:</span>
                      <Badge>1</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Participants:</span>
                      <Badge>0</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Completed:</span>
                      <Badge>0</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Chat Tab */}
        <TabsContent value="chat" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <ChatRoom
              roomId="general"
              roomName="General Discussion"
              roomType="public"
            />
            
            <ChatRoom
              roomId="quiz-help"
              roomName="Quiz Help"
              roomType="quiz"
            />
          </div>
        </TabsContent>

        {/* Testing Tab */}
        <TabsContent value="testing" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Connection Test</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    const socketClient = getSocketClient()
                    toast.info(`Connected: ${socketClient.isConnected()}`)
                  }}
                >
                  Check Connection
                </Button>
                
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    if (typeof window !== 'undefined') {
                      window.location.reload()
                    }
                  }}
                >
                  Reconnect
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Simulation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    // Simulate user joining
                    toast.success('Simulated user joined')
                  }}
                >
                  Simulate User Join
                </Button>
                
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    // Simulate user leaving
                    toast.info('Simulated user left')
                  }}
                >
                  Simulate User Leave
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-2">
                  <div className="flex justify-between">
                    <span>Latency:</span>
                    <Badge variant="outline">~50ms</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Messages/sec:</span>
                    <Badge variant="outline">~100</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Usage:</span>
                    <Badge variant="outline">~2MB</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
