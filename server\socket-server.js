const { createServer } = require('http')
const { Server } = require('socket.io')
const next = require('next')

const dev = process.env.NODE_ENV !== 'production'
const hostname = 'localhost'
const port = process.env.SOCKET_PORT || 3001

// Create Next.js app
const app = next({ dev, hostname, port: 3000 })
const handler = app.getRequestHandler()

// Store connected users
const connectedUsers = new Map()
const activeRooms = new Map()

app.prepare().then(() => {
  const httpServer = createServer((req, res) => {
    handler(req, res)
  })

  const io = new Server(httpServer, {
    cors: {
      origin: process.env.NEXTAUTH_URL || "http://localhost:3000",
      methods: ["GET", "POST"],
      credentials: true
    },
    path: '/socket.io',
    transports: ['websocket', 'polling']
  })

  // Socket connection handling
  io.on('connection', (socket) => {
    console.log('🔌 New socket connection:', socket.id)

    // Handle user authentication
    socket.on('authenticate', (userData) => {
      console.log('🔐 Received authenticate event:', userData)
      try {
        connectedUsers.set(socket.id, {
          socketId: socket.id,
          userId: userData.userId,
          name: userData.name,
          email: userData.email,
          role: userData.role,
          joinedAt: new Date(),
          lastSeen: new Date()
        })

        // Join user-specific and role-based rooms
        socket.join(`user:${userData.userId}`)
        socket.join(`role:${userData.role}`)

        socket.emit('authenticated', {
          success: true,
          userId: userData.userId,
          connectedUsers: connectedUsers.size
        })

        // Notify admins of new user
        socket.to('role:ADMIN').emit('user:joined', {
          userId: userData.userId,
          name: userData.name,
          role: userData.role,
          joinedAt: new Date()
        })

        console.log(`✅ User authenticated: ${userData.name} (${userData.role})`)
      } catch (error) {
        console.error('❌ Authentication error:', error)
        socket.emit('authentication_error', { message: 'Authentication failed' })
      }
    })

    // Handle notifications
    socket.on('send_notification', (data) => {
      const user = connectedUsers.get(socket.id)
      if (!user || user.role !== 'ADMIN') {
        socket.emit('error', { message: 'Unauthorized' })
        return
      }

      const notification = {
        id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: data.type,
        title: data.title,
        message: data.message,
        data: data.data || {},
        createdAt: new Date(),
        senderId: user.userId,
        senderName: user.name
      }

      if (data.broadcast) {
        io.emit('notification', notification)
        console.log(`📢 Notification broadcasted: ${data.title}`)
      } else if (data.targetUserId) {
        io.to(`user:${data.targetUserId}`).emit('notification', notification)
        console.log(`📨 Notification sent to ${data.targetUserId}: ${data.title}`)
      }

      socket.emit('notification_sent', {
        success: true,
        notification,
        target: data.broadcast ? 'all' : data.targetUserId
      })
    })

    // Handle quiz progress
    socket.on('quiz_progress', (data) => {
      const user = connectedUsers.get(socket.id)
      if (!user) return

      const progressUpdate = {
        ...data,
        userId: user.userId,
        userName: user.name,
        timestamp: new Date()
      }

      socket.to(`quiz:${data.quizId}`).emit('quiz_progress_update', progressUpdate)
      socket.to('role:ADMIN').emit('quiz_progress_update', progressUpdate)
    })

    // Handle quiz room management
    socket.on('join_quiz', (quizId) => {
      socket.join(`quiz:${quizId}`)
      
      if (!activeRooms.has(`quiz:${quizId}`)) {
        activeRooms.set(`quiz:${quizId}`, new Set())
      }
      activeRooms.get(`quiz:${quizId}`).add(socket.id)

      console.log(`🎯 User joined quiz room: ${quizId}`)
    })

    socket.on('leave_quiz', (quizId) => {
      socket.leave(`quiz:${quizId}`)
      
      if (activeRooms.has(`quiz:${quizId}`)) {
        activeRooms.get(`quiz:${quizId}`).delete(socket.id)
        if (activeRooms.get(`quiz:${quizId}`).size === 0) {
          activeRooms.delete(`quiz:${quizId}`)
        }
      }

      console.log(`🚪 User left quiz room: ${quizId}`)
    })

    // Handle chat room joining
    socket.on('chat:join', (data) => {
      console.log(`👥 User ${socket.id} joining chat room: ${data.roomId}`)
      socket.join(`chat:${data.roomId}`)

      socket.to(`chat:${data.roomId}`).emit('chat:user_joined', {
        userId: connectedUsers.get(socket.id)?.userId,
        name: connectedUsers.get(socket.id)?.name
      })
    })

    // Handle chat room leaving
    socket.on('chat:leave', (data) => {
      console.log(`👋 User ${socket.id} leaving chat room: ${data.roomId}`)
      socket.leave(`chat:${data.roomId}`)

      socket.to(`chat:${data.roomId}`).emit('chat:user_left', {
        userId: connectedUsers.get(socket.id)?.userId,
        name: connectedUsers.get(socket.id)?.name
      })
    })

    // Handle chat messages
    socket.on('chat:message', (data) => {
      const user = connectedUsers.get(socket.id)
      if (!user) return

      console.log(`💬 Message from ${user.name} in room ${data.roomId}: ${data.message}`)

      const chatMessage = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: user.userId,
        userName: user.name,
        message: data.message,
        type: data.type || 'text',
        timestamp: new Date(),
        roomId: data.roomId
      }

      io.to(`chat:${data.roomId}`).emit('chat:message_received', chatMessage)
    })

    // Handle metrics requests
    socket.on('get_metrics', () => {
      const user = connectedUsers.get(socket.id)
      if (!user || user.role !== 'ADMIN') {
        socket.emit('error', { message: 'Unauthorized' })
        return
      }

      const metrics = {
        connectedUsers: connectedUsers.size,
        activeRooms: activeRooms.size,
        usersByRole: {
          ADMIN: Array.from(connectedUsers.values()).filter(u => u.role === 'ADMIN').length,
          STUDENT: Array.from(connectedUsers.values()).filter(u => u.role === 'STUDENT').length
        },
        timestamp: new Date()
      }

      socket.emit('metrics_update', metrics)
    })

    // Handle disconnection
    socket.on('disconnect', () => {
      const user = connectedUsers.get(socket.id)
      if (user) {
        // Clean up rooms
        activeRooms.forEach((users, roomId) => {
          users.delete(socket.id)
          if (users.size === 0) {
            activeRooms.delete(roomId)
          }
        })

        // Notify admins
        socket.to('role:ADMIN').emit('user:left', {
          userId: user.userId,
          name: user.name,
          role: user.role,
          leftAt: new Date()
        })

        connectedUsers.delete(socket.id)
        console.log(`👋 User disconnected: ${user.name}`)
      }
    })

    // Send connection confirmation
    socket.emit('connected', {
      socketId: socket.id,
      timestamp: new Date()
    })
  })

  // Start server
  httpServer.listen(port, () => {
    console.log(`🚀 Socket.IO server running on http://${hostname}:${port}`)
    console.log(`📡 Socket endpoint: ws://${hostname}:${port}/socket.io`)
  })

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down gracefully')
    httpServer.close(() => {
      console.log('✅ Socket server closed')
      process.exit(0)
    })
  })
})
